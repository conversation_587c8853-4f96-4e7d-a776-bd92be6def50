package net.armcloud.paascenter.openapi.helper.impl;

import net.armcloud.paascenter.common.model.entity.paas.NetStorageRes;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageResUnit;
import net.armcloud.paascenter.openapi.model.entity.NetStorageResUnitDeleted;
import net.armcloud.paascenter.openapi.service.NetStorageResService;
import net.armcloud.paascenter.openapi.service.NetStorageResUnitDeletedService;
import net.armcloud.paascenter.openapi.service.netstorage.NetStorageResUnitService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * NetStoragePadHelperImpl测试类
 * <AUTHOR>
 * @Date 2025/6/25
 * @Description: 测试网络存储删除逻辑改造
 */
@ExtendWith(MockitoExtension.class)
class NetStoragePadHelperImplTest {

    @Mock
    private NetStorageResUnitService netStorageResUnitService;

    @Mock
    private NetStorageResUnitDeletedService netStorageResUnitDeletedService;

    @Mock
    private NetStorageResService netStorageResService;

    @InjectMocks
    private NetStoragePadHelperImpl netStoragePadHelper;

    private NetStorageResUnit mockNetStorageResUnit;
    private NetStorageRes mockNetStorageRes;

    @BeforeEach
    void setUp() {
        // 创建模拟的NetStorageResUnit
        mockNetStorageResUnit = new NetStorageResUnit();
        mockNetStorageResUnit.setNetStorageResUnitId(1L);
        mockNetStorageResUnit.setCustomerId(100L);
        mockNetStorageResUnit.setNetStorageResUnitCode("TEST_CODE_001");
        mockNetStorageResUnit.setClusterCode("CLUSTER_001");
        mockNetStorageResUnit.setPadCode("PAD_001");
        mockNetStorageResUnit.setNetStorageResUnitSize(50L);
        mockNetStorageResUnit.setShutdownFlag(1);

        // 创建模拟的NetStorageRes
        mockNetStorageRes = new NetStorageRes();
        mockNetStorageRes.setNetStorageResId(1L);
        mockNetStorageRes.setCustomerId(100L);
        mockNetStorageRes.setStorageCapacity(1000L);
        mockNetStorageRes.setStorageCapacityUsed(200L);
    }

    @Test
    void testProcessNetStorageResUnitDeleteCallback_Success() {
        // 准备测试数据
        String netStorageResUnitCode = "TEST_CODE_001";

        // 模拟查询存储单元
        when(netStorageResUnitService.getOne(any())).thenReturn(mockNetStorageResUnit);
        
        // 模拟查询存储资源
        when(netStorageResService.list(any())).thenReturn(Collections.singletonList(mockNetStorageRes));
        
        // 模拟保存删除记录
        when(netStorageResUnitDeletedService.save(any(NetStorageResUnitDeleted.class))).thenReturn(true);
        
        // 模拟物理删除
        when(netStorageResUnitService.removeById(anyLong())).thenReturn(true);
        
        // 模拟更新存储容量
        when(netStorageResService.updateById(any(NetStorageRes.class))).thenReturn(true);

        // 执行测试
        netStoragePadHelper.processNetStorageResUnitDeleteCallback(netStorageResUnitCode);

        // 验证调用
        verify(netStorageResUnitService, times(1)).getOne(any());
        verify(netStorageResUnitDeletedService, times(1)).save(any(NetStorageResUnitDeleted.class));
        verify(netStorageResUnitService, times(1)).removeById(1L);
        verify(netStorageResService, times(1)).updateById(any(NetStorageRes.class));
    }

    @Test
    void testProcessNetStorageResUnitBatchDeleteCallback_Success() {
        // 准备测试数据
        List<String> netStorageResUnitCodes = Arrays.asList("TEST_CODE_001", "TEST_CODE_002");
        
        NetStorageResUnit mockNetStorageResUnit2 = new NetStorageResUnit();
        mockNetStorageResUnit2.setNetStorageResUnitId(2L);
        mockNetStorageResUnit2.setCustomerId(100L);
        mockNetStorageResUnit2.setNetStorageResUnitCode("TEST_CODE_002");
        mockNetStorageResUnit2.setClusterCode("CLUSTER_001");
        mockNetStorageResUnit2.setPadCode("PAD_002");
        mockNetStorageResUnit2.setNetStorageResUnitSize(30L);
        mockNetStorageResUnit2.setShutdownFlag(0);

        List<NetStorageResUnit> mockUnits = Arrays.asList(mockNetStorageResUnit, mockNetStorageResUnit2);

        // 模拟批量查询存储单元
        when(netStorageResUnitService.list(any())).thenReturn(mockUnits);
        
        // 模拟查询存储资源
        when(netStorageResService.list(any())).thenReturn(Collections.singletonList(mockNetStorageRes));
        
        // 模拟保存删除记录
        when(netStorageResUnitDeletedService.save(any(NetStorageResUnitDeleted.class))).thenReturn(true);
        
        // 模拟物理删除
        when(netStorageResUnitService.removeById(anyLong())).thenReturn(true);
        
        // 模拟更新存储容量
        when(netStorageResService.updateById(any(NetStorageRes.class))).thenReturn(true);

        // 执行测试
        netStoragePadHelper.processNetStorageResUnitBatchDeleteCallback(netStorageResUnitCodes);

        // 验证调用
        verify(netStorageResUnitService, times(1)).list(any());
        verify(netStorageResUnitDeletedService, times(1)).saveBatch(anyList());
        verify(netStorageResUnitService, times(1)).removeByIds(anyList());
        verify(netStorageResService, times(1)).updateBatchById(anyList());
    }

    @Test
    void testProcessNetStorageResUnitDeleteCallback_NotFound() {
        // 准备测试数据
        String netStorageResUnitCode = "NOT_EXIST_CODE";

        // 模拟查询不到存储单元
        when(netStorageResUnitService.getOne(any())).thenReturn(null);

        // 执行测试
        netStoragePadHelper.processNetStorageResUnitDeleteCallback(netStorageResUnitCode);

        // 验证只调用了查询，没有进行删除操作
        verify(netStorageResUnitService, times(1)).getOne(any());
        verify(netStorageResUnitDeletedService, never()).save(any());
        verify(netStorageResUnitService, never()).removeById(anyLong());
    }

    @Test
    void testProcessNetStorageResUnitBatchDeleteCallback_EmptyList() {
        // 准备测试数据
        List<String> emptyList = Collections.emptyList();

        // 执行测试
        netStoragePadHelper.processNetStorageResUnitBatchDeleteCallback(emptyList);

        // 验证没有进行任何删除操作
        verify(netStorageResUnitService, never()).list(any());
        verify(netStorageResUnitDeletedService, never()).save(any());
        verify(netStorageResUnitService, never()).removeById(anyLong());
    }
}
