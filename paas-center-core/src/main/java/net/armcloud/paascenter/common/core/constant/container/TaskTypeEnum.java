package net.armcloud.paascenter.common.core.constant.container;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TaskTypeEnum {
    DEVICE_CREATE(100),
    DEVICE_DELETE(101),
    DEVICE_RESTART(102),
    DEVICE_CBS_SELF_UPDATE(103),
    DEVICE_BOARD_IMAGE_WARMUP(104),

    INSTANCE_CREATE(200),
    INSTANCE_DELETE(201),
    INSTANCE_RESTART(202),
    INSTANCE_RESET(203),
    INSTANCE_UPGRADE_IMAGE(204),
    INSTANCE_NETWORK_LIMIT(205),
    INSTANCE_BACKUP_DATA(206),
    INSTANCE_RESTORE_BACKUP_DATA(207),
    INSTANCE_SET_ANDROID_PROP(208),
    INSTANCE_REPLACE_PROP(209),
    INSTANCE_MODIFY_PROPERTIES_PROP(210),
    INSTANCE_REPLACE_REAL_ADB(211),
    //网存实例关机
    INSTANCE_NET_WORK_OFF(212),
    //网存实例开机
    INSTANCE_NET_WORK_ON(213),
    //网存实例删除
    INSTANCE_NET_WORK_DELETE(214),
    //网存备份
    INSTANCE_NET_WORK_BACKUP(215),
    //网存删除
    INSTANCE_NET_WORK_RES_UNIT_DELETE(216),
    //网存同步备份
    INSTANCE_NET_SYNC_BACKUP(217),
    INSTANCE_RESTART_GAID(218),
    IMAGE_PUSH(300);
    private final Integer intValue;
}
