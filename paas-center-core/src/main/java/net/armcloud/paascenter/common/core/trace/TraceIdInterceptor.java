// package net.armcloud.paascenter.common.core.trace;
//
// import org.slf4j.MDC;
// import org.springframework.stereotype.Component;
// import org.springframework.web.servlet.HandlerInterceptor;
//
// import javax.servlet.http.HttpServletRequest;
// import javax.servlet.http.HttpServletResponse;
// import java.util.UUID;
//
// @Component
// public class TraceIdInterceptor implements HandlerInterceptor {
//     @Override
//     public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
//         String traceId = request.getHeader("traceId"); // 读取请求头中的 traceId
//         if (traceId == null) {
//             traceId = UUID.randomUUID().toString(); // 生成新的 traceId
//         }
//         MDC.put("traceId", traceId); // 存入 MDC
//         return true;
//     }
//
//     @Override
//     public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
//         MDC.clear(); // 清理 MDC，避免 traceId 泄漏
//     }
// }
