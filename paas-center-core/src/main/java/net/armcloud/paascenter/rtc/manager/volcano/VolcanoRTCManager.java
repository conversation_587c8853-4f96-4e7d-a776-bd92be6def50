package net.armcloud.paascenter.rtc.manager.volcano;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.client.component.CommonPadTaskComponent;
import net.armcloud.paascenter.common.client.internal.dto.command.JoinVolcanoShareRoomCMDDTO;
import net.armcloud.paascenter.common.client.internal.dto.command.PadCMDForwardDTO;
import net.armcloud.paascenter.common.client.internal.vo.CommsTransmissionResultVO;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.entity.paas.PadRoom;
import net.armcloud.paascenter.common.model.entity.paas.PadRtcTokenLog;
import net.armcloud.paascenter.common.model.vo.rtc.RoomTokenVO;
import net.armcloud.paascenter.common.utils.BatchUtils;
import net.armcloud.paascenter.common.volcipaas.service.RtcSdkService;
import net.armcloud.paascenter.commscenter.service.PadCommsDataService;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.rtc.config.VolcengineRTCConfig;
import net.armcloud.paascenter.rtc.exception.code.RtcExceptionCode;
import net.armcloud.paascenter.rtc.mapper.RtcPadRoomMapper;
import net.armcloud.paascenter.rtc.mapper.PadRtcTokenLogMapper;
import net.armcloud.paascenter.rtc.service.strategy.room.model.bo.ApplyShareTokenBO;
import net.armcloud.paascenter.task.config.PullModeConfigHolder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static java.lang.Boolean.TRUE;
import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.JOIN_VOLCANO_SHARE_ROOM;
import static net.armcloud.paascenter.common.enums.SourceTargetEnum.PAAS;
import static net.armcloud.paascenter.rtc.constants.RoomConstants.StreamType.VOLCENGINE_STREAM_TYPE;

@Slf4j
@Component
public class VolcanoRTCManager {
    private final RtcSdkService rtcSdkService;
    private final RtcPadRoomMapper rtcPadRoomMapper;
    private final ApplicationContext applicationContext;
    private final VolcengineRTCConfig volcengineRTCConfig;
    private final PadRtcTokenLogMapper padRtcTokenLogMapper;
    private final PadCommsDataService padCommsDataService;
    private final CommonPadTaskComponent commonPadTaskComponent;
    private final PadMapper padMapper;

    public RoomTokenVO applyVolcanoShareToken(ApplyShareTokenBO bo) {
        long customerId = bo.getCustomerId();
        int expire = bo.getExpire();
        String userId = bo.getUserId();
        AtomicReference<String> appIdAtm = new AtomicReference(volcengineRTCConfig.getAppIdOverseas());
        AtomicReference<String> appKeyAtm =  new AtomicReference(volcengineRTCConfig.getAppKeyOverseas());
        String roomId = bo.getTerminal() + bo.getUserId() + customerId;
        //pc 旧版使用国内流，新版使用海外流
        if(Objects.equals(bo.getTerminal(),"pc") || Objects.equals(bo.getTerminal(),"pc-rtc")){
            if(StringUtils.isEmpty(bo.getStreamVersion())){
                appIdAtm.getAndSet(volcengineRTCConfig.getAppId());
                appKeyAtm.getAndSet(volcengineRTCConfig.getAppKey());
            }
        }

        String appId = appIdAtm.get();
        String appKey = appKeyAtm.get();
        log.info("VolcanoRTCManager.applyVolcanoShareToken. bo:{},appId:{}, appKey:{} ", JSONObject.toJSONString(bo),appId,appKey);
        List<String> padCodes = bo.getPads().stream()
                .map(ApplyShareTokenBO.Pad::getPadCode).collect(Collectors.toList());
        Map<String, String> padCodeRoomCodeMap = rtcPadRoomMapper.listByPadCode(padCodes).stream()
                .collect(Collectors.toMap(PadRoom::getPadCode, PadRoom::getRoomCode, (o1, o2) -> o1));

        List<PadRtcTokenLog> padRtcTokenLogs = new ArrayList<>();
        List<PadCMDForwardDTO.PadInfoDTO> padInfos = new ArrayList<>();
        bo.getPads().forEach(pad -> {
            String padCode = pad.getPadCode();
            PadCMDForwardDTO.PadInfoDTO padInfoDTO = new PadCMDForwardDTO.PadInfoDTO();
            padInfoDTO.setPadCode(padCode);

            JoinVolcanoShareRoomCMDDTO cmdDto = new JoinVolcanoShareRoomCMDDTO();
            cmdDto.setRoomCode(roomId);
            cmdDto.setUserId(padCode);
            cmdDto.setRoomToken(rtcSdkService.getRtcTokenService(appId, appKey, roomId, padCode, expire, TRUE));
            if (TRUE.equals(bo.getPushPublicStream())) {
                cmdDto.setPublicStreamToken(rtcSdkService.getPublicPushStreamToken(appId, appKey, expire, padCode));
            }
            cmdDto.setRtcAppId(appId);
            cmdDto.setPushPublicStream(bo.getPushPublicStream());
            cmdDto.setPullUserId(userId);
            String padCodeRoomCode = padCodeRoomCodeMap.get(padCode);
            cmdDto.setSingleRoomCode(padCodeRoomCode);
            String gameServerRoomToken = rtcSdkService.getRtcTokenService(appId, appKey, padCodeRoomCode,
                    padCode, bo.getExpire(), Boolean.TRUE);
            cmdDto.setSingleRoomToken(gameServerRoomToken);

            JoinVolcanoShareRoomCMDDTO.VideoStream videoStream = new JoinVolcanoShareRoomCMDDTO.VideoStream();
            ApplyShareTokenBO.Pad.VideoStream streamReq = pad.getVideoStream();
            if (streamReq != null) {
                videoStream.setResolution(streamReq.getResolution());
                videoStream.setFrameRate(streamReq.getFrameRate());
                videoStream.setBitrate(streamReq.getBitrate());
            }

            cmdDto.setVideoStream(videoStream);
            padInfoDTO.setData(cmdDto);
            padInfos.add(padInfoDTO);

            PadRtcTokenLog padRtcTokenLog = new PadRtcTokenLog();
            padRtcTokenLog.setRoomId(0L);
            padRtcTokenLog.setPadCode("");
            padRtcTokenLog.setPadCode(padCode);
            padRtcTokenLog.setUserId(userId);
            padRtcTokenLog.setExpire(expire);
            padRtcTokenLog.setRoomToken(cmdDto.getRoomToken());
            padRtcTokenLog.setPublishStream(1);
            padRtcTokenLogs.add(padRtcTokenLog);

            PadRtcTokenLog padRtcSingleRoomTokenLog = new PadRtcTokenLog();
            padRtcSingleRoomTokenLog.setRoomId(0L);
            padRtcSingleRoomTokenLog.setPadCode(padCode);
            padRtcSingleRoomTokenLog.setUserId(userId);
            padRtcSingleRoomTokenLog.setExpire(expire);
            padRtcSingleRoomTokenLog.setRoomToken(gameServerRoomToken);
            padRtcSingleRoomTokenLog.setPublishStream(1);
            padRtcTokenLogs.add(padRtcSingleRoomTokenLog);
        });

        PadCMDForwardDTO request = new PadCMDForwardDTO()
                .setSourceCode(PAAS)
                .setCommand(JOIN_VOLCANO_SHARE_ROOM)
                .setPadInfos(padInfos);

        List<Pad> pads = padMapper.selectPadByPadCodes(padCodes);
        if(CollUtil.isNotEmpty(pads)){
            //拉任务 实例
            List<String> pullPadCodes = new ArrayList<>();
            //推任务 实例
            List<String> pushPadCodes = new ArrayList<>();
            //判断是否有在线的pad，如果都离线直接抛出异常new BasicException(RtcExceptionCode.PUSH_STREAMING_SERVICE_EXCEPTION)，只要有一个pad是在线的则继续执行下面的逻辑
            boolean anyOnline = pads.stream()
                    .anyMatch(Pad::online);
            if (!anyOnline) {
                log.warn("VolcanoRTCManager.applyVolcanoShareToken:所有pad都离线，无法执行操作！request={}", JSON.toJSONString(request));
                throw new BasicException(RtcExceptionCode.PUSH_STREAMING_SERVICE_EXCEPTION);
            }
            for(Pad pad : pads){
                if(pad.getTaskMode() != null && pad.getTaskMode() == 1){
                    pullPadCodes.add(pad.getPadCode());
                }else{
                    pushPadCodes.add(pad.getPadCode());
                }
            }
            if(CollUtil.isNotEmpty(pullPadCodes)){
                commonPadTaskComponent.addPadCMDTask(customerId, pullPadCodes, TaskTypeConstants.GS_JOIN_VOLCANO_SHARE_ROOM, request);
            }
            if(CollUtil.isNotEmpty(pushPadCodes)){
                //深拷贝
                PadCMDForwardDTO padCMDForwardDTO = BeanUtil.copyProperties(request,PadCMDForwardDTO.class);
                try {
                    List<PadCMDForwardDTO.PadInfoDTO> pushPadInfoDTOs = new ArrayList<>();
                    for(PadCMDForwardDTO.PadInfoDTO padInfoDTO : padCMDForwardDTO.getPadInfos()){
                        if(pushPadCodes.contains(padInfoDTO.getPadCode())){
                            pushPadInfoDTOs.add(padInfoDTO);
                        }
                    }
                    padCMDForwardDTO.setPadInfos(pushPadInfoDTOs);
                    List<CommsTransmissionResultVO> result = padCommsDataService.forward(padCMDForwardDTO);
                    result.stream()
                            .map(CommsTransmissionResultVO::getSendSuccess)
                            .filter(Boolean.TRUE::equals).findFirst()
                            .orElseThrow(() -> {
                                log.info("通知gameService加入房间失败！request={},result={}", JSON.toJSONString(padCMDForwardDTO), JSON.toJSONString(result));
                                return new BasicException(RtcExceptionCode.PUSH_STREAMING_SERVICE_EXCEPTION);
                            });
                } catch (BasicException e) {
                    log.warn("通知gameService加入房间失败！request={}", JSON.toJSONString(padCMDForwardDTO), e.getMessage());
                    throw new BasicException(RtcExceptionCode.PUSH_STREAMING_SERVICE_EXCEPTION);
                } catch (Exception e) {
                    log.error("通知gameService加入房间失败！request={}", JSON.toJSONString(padCMDForwardDTO), e);
                    throw new BasicException(RtcExceptionCode.PUSH_STREAMING_SERVICE_EXCEPTION);
                }
            }
        }

        RoomTokenVO roomTokenVO = new RoomTokenVO();
        roomTokenVO.setRoomToken(rtcSdkService.getRtcTokenService(appId, appKey, roomId, userId, expire, Boolean.FALSE));
        roomTokenVO.setRoomCode(roomId);
        roomTokenVO.setAppId(appId);
        roomTokenVO.setStreamType(VOLCENGINE_STREAM_TYPE);

        PadRtcTokenLog padRtcTokenLog = new PadRtcTokenLog();
        padRtcTokenLog.setRoomId(0L);
        padRtcTokenLog.setPadCode("");
        padRtcTokenLog.setUserId(userId);
        padRtcTokenLog.setExpire(expire);
        padRtcTokenLog.setRoomToken(roomTokenVO.getRoomToken());
        padRtcTokenLog.setPublishStream(0);
        padRtcTokenLogs.add(padRtcTokenLog);

        applicationContext.getBean(VolcanoRTCManager.class).asyncSaveLog(padRtcTokenLogs);
        return roomTokenVO;
    }

    @Async
    public void asyncSaveLog(List<PadRtcTokenLog> padRtcTokenLogs) {
        BatchUtils.batchHandling(padRtcTokenLogs, 50, padRtcTokenLogMapper::batchInsert);
    }

    public VolcanoRTCManager(ApplicationContext applicationContext, RtcSdkService rtcSdkService,
                             VolcengineRTCConfig volcengineRTCConfig, PadRtcTokenLogMapper padRtcTokenLogMapper, RtcPadRoomMapper rtcPadRoomMapper,
                             PadCommsDataService padCommsDataService,CommonPadTaskComponent commonPadTaskComponent,PadMapper padMapper) {
        this.applicationContext = applicationContext;
        this.rtcSdkService = rtcSdkService;
        this.volcengineRTCConfig = volcengineRTCConfig;
        this.padRtcTokenLogMapper = padRtcTokenLogMapper;
        this.rtcPadRoomMapper = rtcPadRoomMapper;
        this.padCommsDataService = padCommsDataService;
        this.commonPadTaskComponent = commonPadTaskComponent;
        this.padMapper = padMapper;
    }
}
