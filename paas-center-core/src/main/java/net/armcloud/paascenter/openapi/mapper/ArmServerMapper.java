package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.openapi.model.dto.ArmServerDTO;
import net.armcloud.paascenter.openapi.model.vo.ArmServerVo;
import net.armcloud.paascenter.common.model.entity.paas.ArmServer;
import net.armcloud.paascenter.task.model.vo.BmcBrandInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ArmServerMapper extends BaseMapper<ArmServer> {
    /**
     * 根据用户id匹配它所拥有的arm服务器
     * @param param
     * @return
     */
    List<ArmServerVo> listByArmServerDTO(ArmServerDTO param);

    List<ArmServer> selectByArmIp(String serverIp);


    Integer existChassisLabel(@Param("chassisLabel")String chassisLabel);

    void saveArmServer(ArmServer param);

    void updateArmServer(ArmServer param);


    ArmServer selectByArmServerCode(String model);


    List<ArmServer> armIpList(@Param("armIpList") List<String> armIpList);

    BmcBrandInfo selectBrandInfoByDeviceCode(@Param("deviceCode") String deviceCode);

    BmcBrandInfo selectBrandInfoByArmServer(@Param("armServerCode") String armServerCode);
}
