package net.armcloud.paascenter.openapi.controller;

import net.armcloud.paascenter.common.rocketmq.support.IRocketMqProducerWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/openapi/backend/ops")
public class BackendOpsController {


    @Autowired
    private IRocketMqProducerWrapper rocketMqProducerService;


    @PostMapping("testSendMsg")
    public void testSendMsg() {
        for (int i = 0; i < 50; i++) {
            String msg = "msg_" + i;
           String msgId= rocketMqProducerService.producerNormalMessage("testTopic", "test_tag", msg);
           log.info(">>>> testSendMsg msg = {},msgId={}",msg,msgId);
        }
    }




}
