package net.armcloud.paascenter.openapi.helper.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.client.internal.vo.VirtualizeDeviceInfoVO;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.entity.paas.*;
import net.armcloud.paascenter.openapi.exception.code.PadExceptionCode;
import net.armcloud.paascenter.openapi.helper.NetStoragePadHelper;
import net.armcloud.paascenter.openapi.mapper.DeviceMapper;
import net.armcloud.paascenter.openapi.mapper.ResourceSpecificationMapper;
import net.armcloud.paascenter.openapi.model.dto.netstorage.NetStorageDTO;
import net.armcloud.paascenter.openapi.model.dto.netstorage.NetStorageResPadDeleteDTO;
import net.armcloud.paascenter.openapi.model.dto.netstorage.NetStorageResUnitDeleteDTO;
import net.armcloud.paascenter.openapi.model.entity.NetStorageResUnitDeleted;
import net.armcloud.paascenter.openapi.model.vo.PadDetailsVO;
import net.armcloud.paascenter.openapi.service.INetStorageResOffLogService;
import net.armcloud.paascenter.openapi.service.NetStorageResService;
import net.armcloud.paascenter.openapi.service.NetStorageResUnitDeletedService;
import net.armcloud.paascenter.openapi.service.netstorage.NetStorageComputeUnitPadService;
import net.armcloud.paascenter.openapi.service.netstorage.NetStorageComputeUnitService;
import net.armcloud.paascenter.openapi.service.netstorage.NetStorageResUnitService;
import net.armcloud.paascenter.openapi.utils.IdGeneratorUtils;
import net.armcloud.paascenter.common.utils.CustomerUtils;
import net.armcloud.paascenter.openapi.utils.RequestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static net.armcloud.paascenter.common.core.constant.NumberConsts.ONE;
import static net.armcloud.paascenter.common.core.constant.NumberConsts.ZERO;

/**
 * <AUTHOR>
 * @Date 2025/3/25 10:07
 * @Description:
 */
@Component
@Slf4j
public class NetStoragePadHelperImpl implements NetStoragePadHelper {
    @Resource
    private ResourceSpecificationMapper resourceSpecificationMapper;
    @Resource
    private INetStorageResOffLogService netStorageResOffLogService;
    @Resource
    private NetStorageResService netStorageResService;
    @Resource
    private NetStorageComputeUnitService netStorageComputeUnitService;

    @Resource
    private NetStorageResUnitService netStorageResUnitService;
    @Resource
    private NetStorageComputeUnitPadService netStorageComputeUnitPadService;
    @Resource
    private DeviceMapper deviceMapper;
    @Resource
    private NetStorageResUnitDeletedService netStorageResUnitDeletedService;
    /**
     * 规格缓存
     */
    private final LoadingCache<String, ResourceSpecification> resourceSpecCache = CacheBuilder.newBuilder()
            .expireAfterWrite(1, TimeUnit.MINUTES) // 1 分钟后过期
            .maximumSize(1000) // 限制最多缓存 1000 条
            .build(new CacheLoader<String, ResourceSpecification>() {
                @Override
                public ResourceSpecification load(@NotNull String deviceLevel) {
                    return queryResourceSpecification(deviceLevel);
                }
            });

    private final Cache<Long, VirtualizeDeviceInfoVO> cache = CacheBuilder.newBuilder()
            .maximumSize(10000)  // 最大存储 10000个记录
            .expireAfterWrite(10, TimeUnit.MINUTES)  // 10 分钟后过期
            .build();

    public VirtualizeDeviceInfoVO getVirtualizeDevice(Long deviceId) throws ExecutionException {
        return cache.get(deviceId, () -> deviceMapper.selectListVirtualizeInfoByNetStorageRes(deviceId));
    }

    private ResourceSpecification queryResourceSpecification(String deviceLevel) {
        //实例规格
        return resourceSpecificationMapper.selectOne(new QueryWrapper<ResourceSpecification>().lambda()
                .eq(ResourceSpecification::getSpecificationCode, deviceLevel)
                .eq(ResourceSpecification::getDeleteFlag, ZERO)
                .eq(ResourceSpecification::getStatus, ONE)
                .last("LIMIT 1"));
    }


    @Override
    public List<NetStorageDTO> NetStorageMatchComputeAndStorage(String clusterCode, List<PadDetailsVO> detailsVOList,String countryCode, JSONObject androidProp) {
        List<NetStorageDTO> netStorageDTOList = Lists.newArrayList();
        //根据规格分组
        Map<String, List<PadDetailsVO>> padDetailMap = detailsVOList.stream()
                .collect(Collectors.groupingBy(PadDetailsVO::getDeviceLevel));
        //这里不能通过用户去查找,因为可能是后端管理员操作数据,会操作同一个规格不同用户的板卡。
        padDetailMap.forEach((deviceLevel, padVoList) -> {
            //匹配算力资源
            netStorageDTOList.add(netStorageComputeUnitService.matchUnboundComputeUnitsBySpecAndQuantity(clusterCode, deviceLevel, padVoList,countryCode,androidProp));
        });
        return netStorageDTOList;

    }

    @Override
    public ResourceSpecification getResourceSpecification(String deviceLevel) throws ExecutionException {
        return resourceSpecCache.get(deviceLevel);
    }

    @Override
    public void saveNetStorageDTOList(List<NetStorageDTO> netStorageDTOList) {
        List<NetStorageComputeUnitPad> storageComputeUnitPadList = netStorageDTOList.stream().flatMap(bean -> bean.getNetStorageComputeUnitPadList().stream()).collect(Collectors.toList());
        netStorageComputeUnitPadService.saveBatch(storageComputeUnitPadList);
        //更改算力状态为已绑定
        netStorageDTOList.stream().flatMap(bean -> bean.getNetStorageComputeUnitList().stream()).forEach(netStorageComputeUnit -> {
            //修改成已绑定
            netStorageComputeUnit.setBindFlag(1);
            netStorageComputeUnitService.updateById(netStorageComputeUnit);
            log.info("NetStoragePadHelperImpl_saveNetStorageDTOList setBindFlag netStorageComputeUnit:{}",JSONObject.toJSONString(netStorageComputeUnit));
        });
        //所有被使用的网存code
        List<String> netStorageResCodeList = netStorageDTOList.stream().flatMap(bean -> bean.getNetStorageComputeUnitPadList().stream().map(NetStorageComputeUnitPad::getNetStorageResCode)).collect(Collectors.toList());
        //更新网存实例为已绑定
        netStorageResUnitService.updateBatchBindByCodeList(netStorageResCodeList,1);
    }

    @Override
    public List<NetStorageResUnit> padBackup(List<NetStorageResUnit> netStorageResUnitList,Long totalSize,String clusterCode,Long customerId,String remark) {

        List<NetStorageResUnit> storageResUnitList = netStorageResUnitList.stream().map(netStorageResUnit -> {
            LambdaQueryWrapper<NetStorageResOffLog> logLambdaQueryWrapper = new LambdaQueryWrapper<>();
            logLambdaQueryWrapper.eq(NetStorageResOffLog::getPadCode, netStorageResUnit.getPadCode())
                    .orderByDesc(NetStorageResOffLog::getNetStorageResOffLogId)  // 按 id 倒序排序
                    .last("LIMIT 1");  // 取出第一条记录

            NetStorageResOffLog latestLog = netStorageResOffLogService.getOne(logLambdaQueryWrapper);
            if(Objects.isNull(latestLog)){
                throw  new BasicException("当前有实例没有开机过,无法执行clone");
            }
            NetStorageResUnit neTStorageResUnitCopy = new NetStorageResUnit();
            //取最后一次的IP
            neTStorageResUnitCopy.setDeviceIp(latestLog.getDeviceIp());
            neTStorageResUnitCopy.setCustomerId(netStorageResUnit.getCustomerId());
            neTStorageResUnitCopy.setNetStorageResUnitSize(netStorageResUnit.getNetStorageResUnitSize());
            neTStorageResUnitCopy.setClusterCode(netStorageResUnit.getClusterCode());
            neTStorageResUnitCopy.setNetStorageResUnitUsedSize(netStorageResUnit.getNetStorageResUnitUsedSize());
            neTStorageResUnitCopy.setShutdownFlag(0);
            neTStorageResUnitCopy.setClusterCode(clusterCode);
            neTStorageResUnitCopy.setPadCode(netStorageResUnit.getPadCode());
            neTStorageResUnitCopy.setCreateTime(new Date());
            neTStorageResUnitCopy.setTargetCode(netStorageResUnit.getNetStorageResUnitCode());
            neTStorageResUnitCopy.setRemark(remark);
            neTStorageResUnitCopy.setNetStorageResUnitCode(IdGeneratorUtils.generateStorageId()+"-"+netStorageResUnit.getPadCode());
            return neTStorageResUnitCopy;
        }).collect(Collectors.toList());
        netStorageResUnitService.saveBatch(storageResUnitList);
        netStorageResService.deductsTheSizeOfTheResourcesUsedByResUnit(customerId,netStorageResUnitList);
        return storageResUnitList;
    }

    @Override
    public void processNetStorageBackupCallback(String netStorageResUnitCode) {
        //切割字符串获取真实的code
        NetStorageResUnit one = netStorageResUnitService.getOne(new LambdaQueryWrapper<NetStorageResUnit>().eq(NetStorageResUnit::getNetStorageResUnitCode, netStorageResUnitCode));
        netStorageResUnitService.removeById(one.getNetStorageResUnitId());
        List<NetStorageRes> list = netStorageResService.list(new LambdaQueryWrapper<NetStorageRes>().eq(NetStorageRes::getCustomerId, one.getCustomerId()));
        log.info("current user :{} netStorageResUnitCode:{} the list of network storage instances is:{}",one.getCustomerId(),netStorageResUnitCode,list);
        if(CollectionUtils.isNotEmpty(list)){
            for (NetStorageRes netStorageRes : list) {
                if(netStorageRes.getStorageCapacityUsed()>one.getNetStorageResUnitSize()){
                    //随机扣减一个余额；
                    netStorageRes.setStorageCapacityUsed(netStorageRes.getStorageCapacityUsed()-one.getNetStorageResUnitSize());
                    netStorageResService.updateById(netStorageRes);
                    return ;
                }
            }
        }
    }

    @Override
    public List<NetStorageResUnitDeleteDTO> processNetStoragePadDelete(NetStorageResPadDeleteDTO  param) {
        LambdaQueryWrapper<NetStorageResUnit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NetStorageResUnit::getCustomerId,param.getCustomerId());
        List<NetStorageResUnit> list = netStorageResUnitService.list(queryWrapper);
        if(CollectionUtils.isEmpty(list)){
            throw new BasicException(PadExceptionCode.NETWORK_RES_UNIT_EMPTY);
        }
        Map<String, NetStorageResUnit> netStorageResUnitMap = list.stream()
                .collect(Collectors.toMap(
                        NetStorageResUnit::getNetStorageResUnitCode, // key: netStorageResUnitCode
                        unit -> unit // value: NetStorageResUnit 本身
                ));        //当前拥有的存储单元是否包含本次要删除的存储单元
        if(!netStorageResUnitMap.keySet().containsAll(param.getNetStorageResUnitCodes())){
            log.error("NetStoragePadHelperImpl.processNetStoragePadDelete error. netStorageResUnitCodes is not all in list, netStorageResUnitCodes:{},customerId:{}",param.getNetStorageResUnitCodes(),param.getCustomerId());
            throw new BasicException(PadExceptionCode.NETWORK_RES_UNIT_EMPTY);
        };
        List<NetStorageResUnitDeleteDTO> netStorageResUnitDeleteDTOList = param.getNetStorageResUnitCodes().stream().map(netStorageCode -> {
            LambdaQueryWrapper<NetStorageResOffLog> logLambdaQueryWrapper = new LambdaQueryWrapper<>();
            logLambdaQueryWrapper.eq(NetStorageResOffLog::getPadCode, netStorageResUnitMap.get(netStorageCode).getPadCode())
                    .orderByDesc(NetStorageResOffLog::getNetStorageResOffLogId)  // 按 id 倒序排序
                    .last("LIMIT 1");  // 取出第一条记录
            NetStorageResOffLog latestLog = netStorageResOffLogService.getOne(logLambdaQueryWrapper);
            String clusterCode = deviceMapper.getClusterCodeByIp( latestLog.getDeviceIp());
            NetStorageResUnitDeleteDTO unitDeleteDTO = new NetStorageResUnitDeleteDTO();
            unitDeleteDTO.setResUnitCode(netStorageCode);
            unitDeleteDTO.setDeviceIp(latestLog.getDeviceIp());
            unitDeleteDTO.setClusterCode(clusterCode);
            return unitDeleteDTO;
        }).collect(Collectors.toList());
        return netStorageResUnitDeleteDTOList;
    }

    @Override
    public void processNetStoragePadOff(String padCode) {
        log.info("NetStoragePadHelperImpl.processNetStoragePadOff padCode:{}",padCode);
        LambdaQueryWrapper<NetStorageResUnit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NetStorageResUnit::getPadCode,padCode);
        queryWrapper.eq(NetStorageResUnit::getShutdownFlag,1);
        NetStorageResUnit one = netStorageResUnitService.getOne(queryWrapper);
        //将存储状态打成未开机
        if(Objects.nonNull(one)){
            one.setShutdownFlag(0);
            netStorageResUnitService.updateById(one);
        };
        LambdaQueryWrapper<NetStorageComputeUnitPad> computeUnitWrapper = new LambdaQueryWrapper<>();
        //取最后一条
        computeUnitWrapper.eq(NetStorageComputeUnitPad::getPadCode,padCode).orderByDesc(NetStorageComputeUnitPad::getCreateTime)
        .last("LIMIT 1");
        //获取实例最后一次所在算力位置
        NetStorageComputeUnitPad computeUnitPadServiceOne = netStorageComputeUnitPadService.getOne(computeUnitWrapper);
        //将算力释放
        if(Objects.nonNull(computeUnitPadServiceOne)){
            LambdaQueryWrapper<NetStorageComputeUnit> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(NetStorageComputeUnit::getNetStorageComputeUnitCode,computeUnitPadServiceOne.getNetStorageComputeUnitCode()).last("LIMIT 1");
            NetStorageComputeUnit computeUnit = netStorageComputeUnitService.getOne(wrapper);
            if(Objects.nonNull(computeUnit)){
                computeUnit.setBindFlag(0);
                computeUnit.setUpdateTime(new Date());
                netStorageComputeUnitService.updateById(computeUnit);
                log.info("NetStoragePadHelperImpl_processNetStoragePadOff setBindFlag netStorageComputeUnit:{}",JSONObject.toJSONString(computeUnit));
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processNetStorageResUnitDeleteCallback(String netStorageResUnitCode) {
        log.info("NetStoragePadHelperImpl.processNetStorageResUnitDeleteCallback netStorageResUnitCode:{}",netStorageResUnitCode);
        LambdaQueryWrapper<NetStorageResUnit> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NetStorageResUnit::getNetStorageResUnitCode,netStorageResUnitCode).last("LIMIT 1");
        NetStorageResUnit netStorageResUnit = netStorageResUnitService.getOne(wrapper);
        if(Objects.isNull(netStorageResUnit)){
            log.error("NetStoragePadHelperImpl.processNetStorageResUnitDeleteCallback error. netStorageResUnitCode is null, netStorageResUnitCode:{}",netStorageResUnitCode);
            return;
        }

        // 逻辑删除：将记录移动到删除表中
        logicalDeleteNetStorageResUnit(netStorageResUnit, "单个删除回调");

        // 扣减存储容量
        deductStorageCapacity(netStorageResUnit);

        log.info("NetStoragePadHelperImpl.processNetStorageResUnitDeleteCallback completed successfully. deleted count: 1, input count: 1, netStorageResUnitCode: {}", netStorageResUnitCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processNetStorageResUnitBatchDeleteCallback(List<String> netStorageResUnitCodes) {
        int inputCount = CollectionUtils.isEmpty(netStorageResUnitCodes) ? 0 : netStorageResUnitCodes.size();
        log.info("NetStoragePadHelperImpl.processNetStorageResUnitBatchDeleteCallback started. input count: {}, netStorageResUnitCodes:{}", inputCount, netStorageResUnitCodes);

        if (CollectionUtils.isEmpty(netStorageResUnitCodes)) {
            log.warn("NetStoragePadHelperImpl.processNetStorageResUnitBatchDeleteCallback netStorageResUnitCodes is empty. deleted count: 0, input count: 0");
            return;
        }

        // 批量查询存储单元
        LambdaQueryWrapper<NetStorageResUnit> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(NetStorageResUnit::getNetStorageResUnitCode, netStorageResUnitCodes);
        List<NetStorageResUnit> netStorageResUnits = netStorageResUnitService.list(wrapper);

        int deletedCount = CollectionUtils.isEmpty(netStorageResUnits) ? 0 : netStorageResUnits.size();

        if (CollectionUtils.isEmpty(netStorageResUnits)) {
            log.error("NetStoragePadHelperImpl.processNetStorageResUnitBatchDeleteCallback error. no valid netStorageResUnits found. deleted count: 0, input count: {}, netStorageResUnitCodes:{}", inputCount, netStorageResUnitCodes);
            return;
        }

        // 批量逻辑删除
        batchLogicalDeleteNetStorageResUnits(netStorageResUnits, "客户调用删除接口批量删除");

        // 批量扣减存储容量
        batchDeductStorageCapacity(netStorageResUnits);

        log.info("NetStoragePadHelperImpl.processNetStorageResUnitBatchDeleteCallback completed successfully. deleted count: {}, input count: {}", deletedCount, inputCount);
    }

    /**
     * 批量逻辑删除网络存储单元
     * @param netStorageResUnits 要删除的存储单元列表
     * @param deleteReason 删除原因
     */
    @Transactional(rollbackFor = Exception.class)
    private void batchLogicalDeleteNetStorageResUnits(List<NetStorageResUnit> netStorageResUnits, String deleteReason) {
        try {
            // 批量创建删除记录
            List<NetStorageResUnitDeleted> deletedRecords = Lists.newArrayList();
            List<Long> idsToDelete = Lists.newArrayList();

            for (NetStorageResUnit netStorageResUnit : netStorageResUnits) {
                NetStorageResUnitDeleted deletedRecord = createDeletedRecord(netStorageResUnit, deleteReason);
                deletedRecords.add(deletedRecord);
                idsToDelete.add(netStorageResUnit.getNetStorageResUnitId());
            }

            // 批量保存删除记录
            netStorageResUnitDeletedService.saveBatch(deletedRecords);

            // 批量物理删除原记录
            netStorageResUnitService.removeByIds(idsToDelete);

            log.info("Batch logically deleted {} NetStorageResUnits, reason={}",
                    netStorageResUnits.size(), deleteReason);

        } catch (Exception e) {
            log.error("Failed to batch logically delete NetStorageResUnits, count={}, error={}",
                    netStorageResUnits.size(), e.getMessage(), e);
            throw new RuntimeException("批量逻辑删除网络存储单元失败", e);
        }
    }

    /**
     * 创建删除记录
     * @param netStorageResUnit 存储单元
     * @param deleteReason 删除原因
     * @return 删除记录
     */
    private NetStorageResUnitDeleted createDeletedRecord(NetStorageResUnit netStorageResUnit, String deleteReason) {
        NetStorageResUnitDeleted deletedRecord = new NetStorageResUnitDeleted();
        deletedRecord.setOriginalId(netStorageResUnit.getNetStorageResUnitId());
        deletedRecord.setNetStorageResUnitId(netStorageResUnit.getNetStorageResUnitId());
        deletedRecord.setCustomerId(netStorageResUnit.getCustomerId());
        deletedRecord.setShutdownFlag(netStorageResUnit.getShutdownFlag());
        deletedRecord.setNetStorageResUnitCode(netStorageResUnit.getNetStorageResUnitCode());
        deletedRecord.setClusterCode(netStorageResUnit.getClusterCode());
        deletedRecord.setPadCode(netStorageResUnit.getPadCode());
        deletedRecord.setNetStorageResUnitSize(netStorageResUnit.getNetStorageResUnitSize());

        // 安全地设置可能为null的字段
        setOptionalFields(deletedRecord, netStorageResUnit);

        deletedRecord.setDeletedTime(new Date());

        // 使用CustomerUtils获取当前调用的客户ID
        try {
            Long currentCustomerId = CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest());
            deletedRecord.setDeletedBy(String.valueOf(currentCustomerId));
        } catch (Exception e) {
            log.debug("Failed to get current customer ID, using system as deletedBy: {}", e.getMessage());
            deletedRecord.setDeletedBy("system");
        }

        deletedRecord.setDeleteReason(deleteReason);

        return deletedRecord;
    }

    /**
     * 逻辑删除网络存储单元
     * @param netStorageResUnit 要删除的存储单元
     * @param deleteReason 删除原因
     */
    @Transactional(rollbackFor = Exception.class)
    private void logicalDeleteNetStorageResUnit(NetStorageResUnit netStorageResUnit, String deleteReason) {
        try {
            // 创建删除记录
            NetStorageResUnitDeleted deletedRecord = createDeletedRecord(netStorageResUnit, deleteReason);

            // 保存删除记录
            netStorageResUnitDeletedService.save(deletedRecord);

            // 从原表中物理删除
            netStorageResUnitService.removeById(netStorageResUnit.getNetStorageResUnitId());

            log.info("NetStorageResUnit logically deleted: code={}, reason={}",
                    netStorageResUnit.getNetStorageResUnitCode(), deleteReason);

        } catch (Exception e) {
            log.error("Failed to logically delete NetStorageResUnit: code={}, error={}",
                    netStorageResUnit.getNetStorageResUnitCode(), e.getMessage(), e);
            throw new RuntimeException("逻辑删除网络存储单元失败", e);
        }
    }

    /**
     * 安全地设置可能为null的字段
     * @param deletedRecord 删除记录
     * @param netStorageResUnit 原存储单元
     */
    private void setOptionalFields(NetStorageResUnitDeleted deletedRecord, NetStorageResUnit netStorageResUnit) {
        try {
            deletedRecord.setRemark(netStorageResUnit.getRemark());
        } catch (Exception e) {
            log.debug("remark field not available or error accessing it: {}", e.getMessage());
        }

        try {
            deletedRecord.setCreateTime(netStorageResUnit.getCreateTime());
        } catch (Exception e) {
            log.debug("createTime field not available or error accessing it: {}", e.getMessage());
        }

        try {
            deletedRecord.setUpdateTime(netStorageResUnit.getUpdateTime());
        } catch (Exception e) {
            log.debug("updateTime field not available or error accessing it: {}", e.getMessage());
        }

        try {
            deletedRecord.setCreateBy(netStorageResUnit.getCreateBy());
        } catch (Exception e) {
            log.debug("createBy field not available or error accessing it: {}", e.getMessage());
        }
    }

    /**
     * 批量扣减存储容量
     * @param netStorageResUnits 存储单元列表
     */
    @Transactional(rollbackFor = Exception.class)
    private void batchDeductStorageCapacity(List<NetStorageResUnit> netStorageResUnits) {
        if (CollectionUtils.isEmpty(netStorageResUnits)) {
            return;
        }

        // 按客户ID分组
        Map<Long, List<NetStorageResUnit>> customerGroupMap = netStorageResUnits.stream()
                .collect(Collectors.groupingBy(NetStorageResUnit::getCustomerId));

        for (Map.Entry<Long, List<NetStorageResUnit>> entry : customerGroupMap.entrySet()) {
            Long customerId = entry.getKey();
            List<NetStorageResUnit> customerUnits = entry.getValue();

            // 计算该客户需要扣减的总容量
            long totalDeductSize = customerUnits.stream()
                    .mapToLong(NetStorageResUnit::getNetStorageResUnitSize)
                    .sum();

            // 查询该客户的存储资源
            LambdaQueryWrapper<NetStorageRes> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(NetStorageRes::getCustomerId, customerId);
            List<NetStorageRes> storageResList = netStorageResService.list(queryWrapper);

            if (CollectionUtils.isEmpty(storageResList)) {
                log.error("NetStoragePadHelperImpl.batchDeductStorageCapacity error. storageRes is null, customerId:{}", customerId);
                continue;
            }

            // 扣减存储容量
            long remainingDeductSize = totalDeductSize;
            List<NetStorageRes> toUpdateList = Lists.newArrayList();

            for (NetStorageRes netStorageRes : storageResList) {
                if (remainingDeductSize <= 0) {
                    break;
                }

                long currentUsed = netStorageRes.getStorageCapacityUsed();
                long deductAmount = Math.min(remainingDeductSize, currentUsed);

                if (deductAmount > 0) {
                    netStorageRes.setStorageCapacityUsed(currentUsed - deductAmount);
                    toUpdateList.add(netStorageRes);
                    remainingDeductSize -= deductAmount;

                    log.info("Storage capacity deducted: customerId={}, deducted={}, remaining={}",
                            customerId, deductAmount, netStorageRes.getStorageCapacityUsed());
                }
            }

            // 批量更新存储资源
            if (!toUpdateList.isEmpty()) {
                netStorageResService.updateBatchById(toUpdateList);
            }

            if (remainingDeductSize > 0) {
                // 扣减完成后仍有余量未扣除
                log.warn("Unable to fully deduct storage capacity: customerId={}, remaining={}",
                        customerId, remainingDeductSize);
            }
        }
    }

    /**
     * 扣减存储容量
     * @param netStorageResUnit 存储单元
     */
    @Transactional(rollbackFor = Exception.class)
    private void deductStorageCapacity(NetStorageResUnit netStorageResUnit) {
        LambdaQueryWrapper<NetStorageRes> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NetStorageRes::getCustomerId, netStorageResUnit.getCustomerId());
        List<NetStorageRes> storageResList = netStorageResService.list(queryWrapper);

        // 找不到存储,这里不会出现,为防止脏数据导致的抛错,简单处理下,打印日志
        if (CollectionUtils.isEmpty(storageResList)) {
            log.error("NetStoragePadHelperImpl.deductStorageCapacity error. storageRes is null, netStorageResUnitCode:{}",
                    netStorageResUnit.getNetStorageResUnitCode());
            return;
        }

        for (NetStorageRes netStorageRes : storageResList) {
            // 挑一个有使用额度空间的存储资源,扣减额度
            if (netStorageRes.getStorageCapacityUsed() >= netStorageResUnit.getNetStorageResUnitSize()) {
                netStorageRes.setStorageCapacityUsed(netStorageRes.getStorageCapacityUsed() - netStorageResUnit.getNetStorageResUnitSize());
                netStorageResService.updateById(netStorageRes);
                log.info("Storage capacity deducted: customerId={}, deducted={}, remaining={}",
                        netStorageResUnit.getCustomerId(),
                        netStorageResUnit.getNetStorageResUnitSize(),
                        netStorageRes.getStorageCapacityUsed());
                break;
            }
        }
    }

    @Override
    public void onlyNetStoragePadOff(String padCode) {
        LambdaQueryWrapper<NetStorageResUnit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NetStorageResUnit::getPadCode,padCode);
        queryWrapper.eq(NetStorageResUnit::getShutdownFlag,1);
        NetStorageResUnit one = netStorageResUnitService.getOne(queryWrapper);
        //将存储状态打成未开机
        if(Objects.nonNull(one)){
            one.setShutdownFlag(0);
            netStorageResUnitService.updateById(one);
        }
    }

}


