package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.openapi.model.vo.PadGroupVO;
import net.armcloud.paascenter.common.model.entity.paas.PadGroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PadGroupMapper extends BaseMapper<PadGroup> {

    /**
     * 查询实例分组列表
     *
     * @param customerId 客户ID
     * @param groupIds   分组ID
     * @return List<PadGroupVO>
     */
    List<PadGroupVO> selectPadGroupListVO(@Param("customerId") Long customerId, @Param("groupIds") List<Integer> groupIds,@Param("padCode")String padCode);

    /**
     * 查询实例分组下的实例数量
     *
     * @param id 分组ID
     * @return int
     */
    int countPadByGroupId(@Param("id") Integer id);

    int countPadByGroupIdAndGroupName(@Param("groupId") Long groupId, @Param("groupName") String groupName);
}
