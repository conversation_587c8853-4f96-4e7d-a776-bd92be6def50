package net.armcloud.paascenter.task.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paascenter.cms.model.request.VirtualizeDeviceRequest;
import net.armcloud.paascenter.common.client.internal.dto.AddBackupTaskDTO;
import net.armcloud.paascenter.common.client.internal.dto.RestoreBackupTaskDTO;
import net.armcloud.paascenter.common.client.internal.dto.TaskTypeAndStatusDTO;
import net.armcloud.paascenter.common.client.internal.dto.UpdatePadTaskByWsDTO;
import net.armcloud.paascenter.common.client.internal.vo.AddDeviceTaskVO;
import net.armcloud.paascenter.common.client.internal.vo.AddPadTaskVO;
import net.armcloud.paascenter.common.client.internal.vo.VirtualizeDeviceInfoVO;
import net.armcloud.paascenter.common.core.constant.container.TaskTypeEnum;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.core.domain.Page;
import net.armcloud.paascenter.common.model.dto.api.*;
import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetail;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.entity.task.PadTask;
import net.armcloud.paascenter.common.model.entity.task.Task;
import net.armcloud.paascenter.common.model.entity.task.TaskTimeoutConfig;
import net.armcloud.paascenter.common.model.vo.api.*;
import net.armcloud.paascenter.common.model.vo.task.PadTaskCallbackVO;
import net.armcloud.paascenter.common.model.vo.task.UpdatePadResetAndRestartVO;
import net.armcloud.paascenter.task.enums.TaskTypeAndChannelEnum;
import net.armcloud.paascenter.task.model.dto.PullTaskHealthDTO;
import net.armcloud.paascenter.task.model.dto.PullTaskResultDTO;

import java.util.List;

public interface ITaskService extends IService<Task> {
    /**
     * 获取客户任务ID
     *
     * @param customerId
     * @return 任务ID
     */
    Integer getCustomerTaskId(Long customerId);

    /**
     * 获取客户任务ID
     * @param customerId
     * @return
     */
    Integer getMultipleCustomerTaskId(Long customerId, int num);


    /**
     * 任务详情
     *
     * @param taskDetailsDTO taskDetailsDTO
     * @return TaskDetailsVO
     */
    TaskDetailsVO taskDetailsService(TaskDetailsDTO taskDetailsDTO);

    /**
     * 添加主任务
     *
     * @param type       任务类型
     * @param status     任务状态
     * @param customerId 客户ID
     * @param sourceCode 客户ID
     * @return taskId
     */
    Task addTaskService(Integer type, Integer status, Long customerId, String sourceCode, String createBy);

    /**
     * 添加主任务(新)
     *
     * @param type       任务类型
     * @param status     任务状态
     * @param customerId 客户ID
     * @param sourceCode 客户ID
     * @return taskId
     */
    Task addNewTaskService(Integer type, Integer status, Long customerId, String sourceCode, String createBy,List<String> padCodes);

    /**
     * 添加pad任务
     *
     * @param addTaskDTO addTaskDTO
     * @return taskId
     */
    List<AddPadTaskVO> addPadTaskService(AddPadTaskDTO addTaskDTO);

    /**
     * 添加pad任务 - 拉模式
     *
     * @param addTaskDTO addTaskDTO
     * @return taskId
     */
    List<AddPadTaskVO> addPadTaskServicePullMode(AddPadTaskDTO addTaskDTO);

    /**
     * 添加物理机任务
     *
     * @param addDeviceTaskDTO
     * @return
     */
    List<AddDeviceTaskVO> addDeviceTaskService(AddDeviceTaskDTO addDeviceTaskDTO);

    /**
     * @param updateTaskDTO updateTaskDTO
     * @return Boolean
     */
    Boolean updateTaskService(UpdateTaskDTO updateTaskDTO);

    /**
     * 删除任务
     *
     * @param deleteTaskDTO deleteTaskDTO
     * @return Boolean
     */
    Boolean deleteTaskService(DeleteTaskDTO deleteTaskDTO);

    /**
     * 修改实例重启/重置任务状态
     *
     * @param updatePadTaskDTO updatePadTaskDTO
     * @return Boolean
     */
    UpdatePadResetAndRestartVO updatePadResetAndRestart(UpdatePadTaskDTO updatePadTaskDTO);

    /**
     * 更新子任务状态
     * <p>
     * 主任务状态根据子任务状态联动更新
     */
    void updateSubTaskStatus(UpdateSubTaskDTO dto);

    /**
     * 查询任务超时配置列表
     */
    List<TaskTimeoutConfig> listTimeoutConfig();

    /**
     * 查询padTask任务
     */
    PadTaskCallbackVO padTaskByCodeService(String padCode);

    void updatePadTaskByWsConnected(UpdatePadTaskByWsDTO dto);

    void updateDeviceTaskByWsConnected(UpdatePadTaskByWsDTO dto);

    /**
     * 获取实例操作任务详情
     */
    List<PadTaskViewVO> padTaskDetailsService(TaskDetailsInfoDTO taskDetailsDTO);

    /**
     * 获取文件任务详情
     */
    List<FileTaskViewVO> fileTaskDetailsService(TaskDetailsInfoDTO taskDetailsDTO);

    /**
     * 更新容器任务状态
     *
     * @param dto
     * @return
     */
    Task updateContainerDeviceTaskResult(ContainerTaskResultVO dto);

    /**
     * @param dto
     * @return
     */
    Task updatePadTaskCallback(ContainerTaskResultVO dto);

    Task updatePadTaskCallbackTypeOn(Task task,ContainerTaskResultVO dto);

    /**
     * 修改物理机任务
     *
     * @param deviceTasks
     * @return
     */
    Boolean updateDeviceTaskResult(List<AddDeviceTaskVO> deviceTasks);


    List<PadTask> selectTaskByTaskTypeAndTaskStatus(List<Pad> pads, List<Integer> status);

    /**
     * 修改容器任务
     *
     * @param dto
     * @return
     */
    Boolean updateContainerInstanceTaskResult(ContainerInstanceTaskResultDTO dto);

    void addDeviceTaskBmcTaskId(List<BmcTaskInfoVO> bmcTaskInfoVOs);

    List<PadTask> addBackupTask(AddBackupTaskDTO param);

    List<PadTask> addRestoreTask(RestoreBackupTaskDTO param);


    /**
     * 查询任务执行数量
     * @return
     */
    long countDeviceTask(TaskTypeAndStatusDTO dto);

    /**
     * 容器任务与实例任务关联
     *
     * @param dto
     * @return
     */
    Boolean updatePadTaskByContainerTask(ContainerPadTaskRequestDTO dto);

    /**
     * 保存任务关联实例信息详情
     * @param subTaskId
     * @param taskTypeEnum
     * @param device
     */
    void saveDeviceInstance(Long taskId, Long subTaskId, TaskTypeEnum taskTypeEnum, VirtualizeDeviceRequest.Device device, VirtualizeDeviceInfoVO virtualizeDeviceInfoVO,Boolean clearContainerData);

    /**
     * 保存任务关联实例信息详情成功表
     * @param subTaskId
     * @param taskTypeAndChannelEnum
     */
    void saveDeviceInstanceSucc(Long subTaskId, TaskTypeAndChannelEnum taskTypeAndChannelEnum);

    /**
     * 保存任务关联实例信息详情
     * @param subTaskId
     * @param taskTypeEnum
     * @param obj
     */
    void saveDeviceInstanceSingle(Long taskId, Long subTaskId, TaskTypeEnum taskTypeEnum, TaskRelInstanceDetail taskRelInstanceDetail, Object obj);

    /**
     * 设备离线 清除各自类型的任务
     * @param pullTaskHealthDTO
     */
    void offlineCleanTask(PullTaskHealthDTO pullTaskHealthDTO);

    /**
     * 更新bmc任务
     * @param pullTaskResultDTO
     */
    void updateBmcTask(PullTaskResultDTO pullTaskResultDTO);

    /**
     * 添加pad任务(gs任务直发) - 拉模式 - 非标准格式
     * 目前上传预览图使用
     *
     * @param addTaskDTO addTaskDTO
     * @return taskId
     */
    List<AddPadTaskVO> addGsDirectPadTaskServicePullMode(AddPadTaskDTO addTaskDTO);

    /**
     * 添加pad任务(gs任务直发) - 拉模式 - 标准格式
     * 其他需要的指令 应调用这个方法
     * @param addTaskDTO addTaskDTO
     * @return taskId
     */
    List<AddPadTaskVO> addStandardGsDirectPadTaskServicePullMode(AddPadTaskDTO addTaskDTO);
}
