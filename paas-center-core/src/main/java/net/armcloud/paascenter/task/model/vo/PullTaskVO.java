package net.armcloud.paascenter.task.model.vo;

import lombok.Data;

import java.util.List;

/**
 * 拉取任务请求对象
 */
@Data
public class PullTaskVO {
    /**剩余任务数量*/
    private Long surplusTaskCount;

    /**服务器时间*/
    private String serverTime;

    /**任务信息*/
    private List<TaskInfo> taskInfos;

    @Data
    public static class TaskInfo {

        /**任务id*/
        private Long taskId;
        /**任务类型 TaskTypeConstants*/
        private Integer taskType;
        /**优先级 0-9 数字越大 优先级越高*/
        private Integer priority;
        /**任务创建时间戳*/
        private Long createTime;
        /**任务参数*/
        private Object taskParam;
        /**厂商参数*/
        private BmcBrandInfo bmcBrandInfo;
    }

}
