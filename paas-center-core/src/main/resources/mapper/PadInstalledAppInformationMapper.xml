<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.PadInstalledAppInformationMapper">
  <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.PadInstalledAppInformation">
    <id column="id" property="id" />
    <result column="pad_code" property="padCode" />
    <result column="apps_json" property="appsJSON" />
    <result column="create_by" property="createBy" />
    <result column="create_time" property="createTime" />
    <result column="update_by" property="updateBy" />
    <result column="update_time" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, pad_code, apps_json
  </sql>

  <select id="getByPodCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from pad_installed_app_information
    where pad_code = #{padCode}
    order by id desc
    limit 1
  </select>
</mapper>