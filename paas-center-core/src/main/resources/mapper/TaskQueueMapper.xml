<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.task.mapper.TaskQueueMapper">
  <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.task.TaskQueue">
    <id column="id" property="id" />
    <result column="key" property="key" />
    <result column="content_json" property="contentJson" />
    <result column="customer_id" property="customerId" />
    <result column="task_type" property="taskType" />
    <result column="master_task_id" property="masterTaskId" />
    <result column="sub_task_id" property="subTaskId" />
    <result column="status" property="status" />
    <result column="create_by" property="createBy" />
    <result column="create_time" property="createTime" />
    <result column="update_by" property="updateBy" />
    <result column="update_time" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, `key`, content_json, customer_id, task_type, master_task_id, sub_task_id, `status`,
    create_by, create_time, update_by, update_time
  </sql>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.armcloud.paascenter.common.model.entity.task.TaskQueue" useGeneratedKeys="true">
    insert into task_queue
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="key != null">
        `key`,
      </if>
      <if test="contentJson != null">
        content_json,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="taskType != null">
        task_type,
      </if>
      <if test="masterTaskId != null">
        master_task_id,
      </if>
      <if test="subTaskId != null">
        sub_task_id,
      </if>
      <if test="status != null">
        `status`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="key != null">
        #{key},
      </if>
      <if test="contentJson != null">
        #{contentJson},
      </if>
      <if test="customerId != null">
        #{customerId},
      </if>
      <if test="taskType != null">
        #{taskType},
      </if>
      <if test="masterTaskId != null">
        #{masterTaskId},
      </if>
      <if test="subTaskId != null">
        #{subTaskId},
      </if>
      <if test="status != null">
        #{status},
      </if>
    </trim>
  </insert>

  <update id="updateStatusByKeyAndSubtaskId">
    update task_queue
    set status = #{status}
    where `key` = #{key}
      and sub_task_id = #{subTaskId}
    and status &lt; #{status}
  </update>
    <select id="selectContentJsonOne" resultType="java.lang.String">
      select content_json FROM task_queue where master_task_id = #{masterTaskId} and `key`  = #{key}
    </select>


  <select id="getByMasterAndSubTaskId" resultMap="BaseResultMap">
    select *
    from task_queue
    where master_task_id = #{masterTaskId}
      and sub_task_id = #{subTaskId}
  </select>

</mapper>