# NetStorageResUnit删除逻辑改造说明

## 改造概述

基于`NetStoragePadHelperImpl#processNetStorageResUnitDeleteCallback`方法进行逻辑改造，主要包括：

1. **支持批量操作**：新增批量删除回调方法
2. **物理删除改为逻辑删除**：创建删除记录表，将删除的记录移动到专门的删除表中

## 改造内容

### 1. 数据库表结构

#### 新增删除记录表：`net_storage_res_unit_deleted`

```sql
CREATE TABLE `net_storage_res_unit_deleted` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `original_id` BIGINT NOT NULL COMMENT '原记录ID',
    `net_storage_res_unit_id` BIGINT NOT NULL COMMENT '网络存储详情ID',
    `customer_id` BIGINT NOT NULL COMMENT '客户ID',
    `shutdown_flag` INT DEFAULT 0 COMMENT '是否有实例开关机 0: 关机,1: 开机',
    `net_storage_res_unit_code` VARCHAR(255) NOT NULL COMMENT '网络存储详情Code',
    `cluster_code` VARCHAR(255) NOT NULL COMMENT '集群Code',
    `pad_code` VARCHAR(255) NOT NULL COMMENT '实例Code',
    `net_storage_res_unit_size` BIGINT NOT NULL COMMENT '网络存储大小(单位:GB)',
    `remark` VARCHAR(500) COMMENT '备注',
    `create_time` DATETIME COMMENT '原记录创建时间',
    `update_time` DATETIME COMMENT '原记录更新时间',
    `create_by` VARCHAR(255) COMMENT '原记录创建者',
    `update_by` VARCHAR(255) COMMENT '原记录更新者',
    `deleted_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '删除时间',
    `deleted_by` VARCHAR(255) COMMENT '删除操作者',
    `delete_reason` VARCHAR(500) COMMENT '删除原因',
    PRIMARY KEY (`id`),
    INDEX `idx_original_id` (`original_id`),
    INDEX `idx_customer_id` (`customer_id`),
    INDEX `idx_net_storage_res_unit_code` (`net_storage_res_unit_code`),
    INDEX `idx_deleted_time` (`deleted_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='网络存储单元删除记录表';
```

### 2. 新增类文件

#### 实体类
- `NetStorageResUnitDeleted.java` - 删除记录实体类

#### Mapper
- `NetStorageResUnitDeletedMapper.java` - 删除记录Mapper接口

#### Service
- `NetStorageResUnitDeletedService.java` - 删除记录Service接口
- `NetStorageResUnitDeletedServiceImpl.java` - 删除记录Service实现类

### 3. 接口方法变更

#### NetStoragePadHelper接口新增方法

```java
/**
 * 处理网存存储批量删除成功回调
 * @param netStorageResUnitCodes 网存存储单元编码集合
 */
void processNetStorageResUnitBatchDeleteCallback(List<String> netStorageResUnitCodes);
```

#### NetStoragePadHelperImpl实现类变更

1. **原有方法改造**：`processNetStorageResUnitDeleteCallback`
   - 物理删除改为逻辑删除
   - 将删除记录保存到删除表中
   - 从原表中物理删除记录

2. **新增方法**：`processNetStorageResUnitBatchDeleteCallback`
   - 支持批量删除操作
   - 批量处理删除记录

3. **新增私有方法**：
   - `logicalDeleteNetStorageResUnit` - 逻辑删除单个存储单元
   - `deductStorageCapacity` - 扣减存储容量

## 使用方式

### 单个删除（原有方式）

```java
// 原有调用方式保持不变
netStoragePadHelper.processNetStorageResUnitDeleteCallback("NET_STORAGE_CODE_001");
```

### 批量删除（新增方式）

```java
// 批量删除调用
List<String> codes = Arrays.asList("NET_STORAGE_CODE_001", "NET_STORAGE_CODE_002", "NET_STORAGE_CODE_003");
netStoragePadHelper.processNetStorageResUnitBatchDeleteCallback(codes);
```

## 逻辑删除流程

1. **查询原记录**：根据存储单元编码查询原始记录
2. **创建删除记录**：将原记录信息复制到删除记录表
3. **保存删除记录**：将删除记录保存到`net_storage_res_unit_deleted`表
4. **物理删除原记录**：从原表中删除记录
5. **扣减存储容量**：更新相关存储资源的使用容量

## 错误处理

- 如果存储单元不存在，记录错误日志并返回
- 如果批量删除列表为空，记录警告日志并返回
- 删除过程中出现异常，抛出运行时异常并记录详细错误信息

## 测试

提供了完整的单元测试用例：
- 单个删除成功测试
- 批量删除成功测试
- 删除不存在记录测试
- 空列表批量删除测试

## 注意事项

1. **数据一致性**：删除操作在事务中执行，确保数据一致性
2. **性能考虑**：批量删除时逐个处理，避免大批量操作影响性能
3. **日志记录**：详细记录删除操作的日志，便于问题排查
4. **字段兼容性**：对于可能不存在的字段（如remark、createTime等），使用try-catch进行安全访问

## 部署说明

1. 执行SQL脚本创建删除记录表
2. 部署新的代码版本
3. 验证删除功能是否正常工作
4. 监控删除记录表的数据增长情况
